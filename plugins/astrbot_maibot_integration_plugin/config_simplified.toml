# AstrBot精简集成插件配置文件
# 专注于核心功能的精简配置

[plugin]
# 插件基础配置
name = "astrbot_simplified"
version = "1.0.0"
enabled = true
debug_mode = false

[astrbot_service]
# AstrBot服务配置
enable_llm = true
default_provider = "openai"
default_model = "gpt-3.5-turbo"

# AstrBot服务地址（如果AstrBot独立运行）
service_host = "localhost"
service_port = 6185
service_timeout = 30

# 支持的LLM提供商列表（精简版只配置常用的）
[astrbot_service.llm_providers]
openai = { enabled = true, priority = 1 }
claude = { enabled = true, priority = 2 }
gemini = { enabled = true, priority = 3 }
qwen = { enabled = true, priority = 4 }
baidu = { enabled = true, priority = 5 }

[system]
# 系统配置
log_level = "INFO"
max_message_length = 4000
response_timeout = 30

# 性能配置
max_concurrent_requests = 10
cache_enabled = true
cache_ttl = 300

[security]
# 安全配置
enable_permission_check = true
allowed_users = []  # 空数组表示允许所有用户
blocked_users = []

# 命令权限
[security.command_permissions]
astrbot_status = "all"      # 所有用户可用
astrbot_control = "admin"   # 仅管理员可用
