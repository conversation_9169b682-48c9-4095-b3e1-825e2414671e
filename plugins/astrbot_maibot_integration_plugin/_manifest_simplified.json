{"plugin_name": "astrbot_simplified", "display_name": "AstrBot精简集成插件", "version": "1.0.0", "description": "AstrBot与MaiBot的精简集成插件，专注于核心功能", "author": "AstrBot Team", "license": "MIT", "homepage": "https://github.com/soulter/AstrBot", "repository": "https://github.com/soulter/AstrBot", "tags": ["<PERSON><PERSON><PERSON>", "llm", "integration", "ai"], "category": "integration", "main_file": "plugin_simplified.py", "config_file": "config.toml", "min_maibot_version": "0.9.0", "python_version": ">=3.8", "components": {"actions": [{"name": "astrbot_llm", "class": "AstrBotLLMAction", "description": "调用AstrBot的LLM服务", "activation_type": "keyword", "keywords": ["ai", "llm", "chat"]}], "commands": [{"name": "astrbot_status", "class": "AstrBotStatusCommand", "description": "查询AstrBot系统状态"}, {"name": "astrbot_control", "class": "AstrBotControlCommand", "description": "AstrBot系统控制"}]}, "features": ["21个LLM提供商统一接口", "AstrBot系统状态监控", "基础系统控制功能", "精简架构设计", "低资源占用"], "dependencies": {"python_packages": [], "system_packages": [], "maibot_plugins": []}, "permissions": ["message_read", "message_send", "system_info"], "configuration": {"astrbot_service": {"enable_llm": {"type": "boolean", "default": true, "description": "是否启用LLM服务"}, "default_provider": {"type": "string", "default": "openai", "description": "默认LLM提供商"}, "default_model": {"type": "string", "default": "gpt-3.5-turbo", "description": "默认LLM模型"}}}}