# -*- coding: utf-8 -*-
"""
AstrBot服务接口层

为AstrBot核心功能提供统一的服务接口，支持：
1. LLM服务调用（21个提供商）
2. 系统状态查询
3. 基础配置管理

这是精简版本，专注于核心功能。
"""

import asyncio
import aiohttp
import logging
import json
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import toml

logger = logging.getLogger(__name__)

class AstrBotServiceInterface:
    """
    AstrBot服务接口
    
    提供对AstrBot核心功能的统一访问接口
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化服务接口"""
        self.config = self._load_config(config_path)
        self.base_url = f"http://{self.config['host']}:{self.config['port']}"
        self.timeout = self.config.get('timeout', 30)
        self.session = None
        
        logger.info(f"🔗 AstrBot服务接口初始化: {self.base_url}")
    
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载配置"""
        if config_path is None:
            config_path = Path(__file__).parent / "config_simplified.toml"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = toml.load(f)
            
            return {
                'host': config['astrbot_service']['service_host'],
                'port': config['astrbot_service']['service_port'],
                'timeout': config['astrbot_service']['service_timeout'],
                'default_provider': config['astrbot_service']['default_provider'],
                'default_model': config['astrbot_service']['default_model']
            }
        except Exception as e:
            logger.warning(f"配置加载失败，使用默认配置: {e}")
            return {
                'host': 'localhost',
                'port': 6185,
                'timeout': 30,
                'default_provider': 'openai',
                'default_model': 'gpt-3.5-turbo'
            }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, endpoint: str, method: str = "GET", data: Optional[Dict] = None) -> Dict[str, Any]:
        """发起HTTP请求"""
        if not self.session:
            raise RuntimeError("服务接口未初始化，请使用async with语句")
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    return await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    return await response.json()
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
                
        except aiohttp.ClientError as e:
            logger.error(f"请求失败 {url}: {e}")
            return {"error": f"网络请求失败: {e}"}
        except Exception as e:
            logger.error(f"请求异常 {url}: {e}")
            return {"error": f"请求异常: {e}"}
    
    # ========================================================================
    # 核心服务方法
    # ========================================================================
    
    async def call_llm_service(self, 
                              message: str, 
                              provider: Optional[str] = None,
                              model: Optional[str] = None,
                              **kwargs) -> Dict[str, Any]:
        """
        调用LLM服务
        
        Args:
            message: 用户消息
            provider: LLM提供商（可选）
            model: 模型名称（可选）
            **kwargs: 其他参数
            
        Returns:
            Dict: LLM响应结果
        """
        provider = provider or self.config['default_provider']
        model = model or self.config['default_model']
        
        request_data = {
            "messages": [{"role": "user", "content": message}],
            "provider": provider,
            "model": model,
            **kwargs
        }
        
        logger.info(f"🤖 调用LLM服务: {provider}/{model}")
        result = await self._make_request("/api/llm/chat", "POST", request_data)
        
        if "error" not in result:
            logger.info("✅ LLM服务调用成功")
        else:
            logger.error(f"❌ LLM服务调用失败: {result['error']}")
        
        return result
    
    async def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            Dict: 系统状态信息
        """
        logger.info("📊 查询系统状态")
        result = await self._make_request("/api/system/status", "GET")
        
        if "error" not in result:
            logger.info("✅ 系统状态查询成功")
        else:
            logger.error(f"❌ 系统状态查询失败: {result['error']}")
        
        return result
    
    async def get_llm_providers(self) -> Dict[str, Any]:
        """
        获取可用的LLM提供商列表
        
        Returns:
            Dict: LLM提供商信息
        """
        logger.info("📋 查询LLM提供商")
        result = await self._make_request("/api/llm/providers", "GET")
        
        if "error" not in result:
            logger.info("✅ LLM提供商查询成功")
        else:
            logger.error(f"❌ LLM提供商查询失败: {result['error']}")
        
        return result
    
    async def execute_system_command(self, command: str, **kwargs) -> Dict[str, Any]:
        """
        执行系统命令
        
        Args:
            command: 命令名称
            **kwargs: 命令参数
            
        Returns:
            Dict: 命令执行结果
        """
        request_data = {
            "command": command,
            "parameters": kwargs
        }
        
        logger.info(f"⚙️ 执行系统命令: {command}")
        result = await self._make_request("/api/system/command", "POST", request_data)
        
        if "error" not in result:
            logger.info("✅ 系统命令执行成功")
        else:
            logger.error(f"❌ 系统命令执行失败: {result['error']}")
        
        return result
    
    async def get_plugin_info(self) -> Dict[str, Any]:
        """
        获取插件系统信息
        
        Returns:
            Dict: 插件系统信息
        """
        logger.info("🔌 查询插件信息")
        result = await self._make_request("/api/plugins/info", "GET")
        
        if "error" not in result:
            logger.info("✅ 插件信息查询成功")
        else:
            logger.error(f"❌ 插件信息查询失败: {result['error']}")
        
        return result

# ============================================================================
# 便捷函数
# ============================================================================

async def create_service_interface(config_path: Optional[str] = None) -> AstrBotServiceInterface:
    """创建服务接口实例"""
    return AstrBotServiceInterface(config_path)

async def quick_llm_call(message: str, provider: str = "openai") -> str:
    """快速LLM调用"""
    async with AstrBotServiceInterface() as service:
        result = await service.call_llm_service(message, provider=provider)
        
        if "error" in result:
            return f"❌ 调用失败: {result['error']}"
        
        # 提取响应内容
        if "response" in result:
            return result["response"]
        elif "content" in result:
            return result["content"]
        else:
            return str(result)

async def quick_status_check() -> Dict[str, Any]:
    """快速状态检查"""
    async with AstrBotServiceInterface() as service:
        return await service.get_system_status()

# ============================================================================
# 测试函数
# ============================================================================

async def test_service_interface():
    """测试服务接口"""
    logger.info("🧪 开始测试AstrBot服务接口")
    
    try:
        async with AstrBotServiceInterface() as service:
            # 测试系统状态
            status = await service.get_system_status()
            logger.info(f"系统状态: {status}")
            
            # 测试LLM提供商
            providers = await service.get_llm_providers()
            logger.info(f"LLM提供商: {providers}")
            
            # 测试LLM调用
            llm_result = await service.call_llm_service("Hello, AstrBot!")
            logger.info(f"LLM响应: {llm_result}")
            
        logger.info("✅ 服务接口测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 服务接口测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_service_interface())
