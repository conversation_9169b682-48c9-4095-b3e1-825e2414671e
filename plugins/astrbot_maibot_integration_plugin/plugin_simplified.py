# -*- coding: utf-8 -*-
"""
AstrBot-MaiBot精简集成插件

专注于核心功能的精简版本，移除所有重复代码和过度复杂的设计。
只保留最核心的3个组件：LLM服务、状态查询、基础控制。

核心功能：
- LLM服务调用（支持21个提供商）
- AstrBot状态查询
- 基础系统控制
"""

import sys
import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any, Union, Tuple, Type

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 动态路径检测
def setup_paths():
    """设置项目路径"""
    current_file = Path(__file__)
    maibot_root = current_file.parent.parent.parent
    
    if str(maibot_root) not in sys.path:
        sys.path.insert(0, str(maibot_root))
    
    return maibot_root

# 设置路径
maibot_root = setup_paths()

# 导入MaiBot插件系统
try:
    from src.plugin_system.base.base_plugin import BasePlugin
    from src.plugin_system.base.base_action import BaseAction
    from src.plugin_system.base.base_command import BaseCommand
    from src.plugin_system.base.component_types import (
        ActionInfo, CommandInfo, ActionActivationType, ChatMode
    )
    from src.plugin_system.apis.plugin_register_api import register_plugin
    
    MAIBOT_AVAILABLE = True
    logger.info("✅ MaiBot插件系统导入成功")

except ImportError as e:
    logger.warning(f"⚠️ MaiBot插件系统不可用: {e}")
    MAIBOT_AVAILABLE = False
    
    # 占位符类
    class BasePlugin:
        def __init__(self, *args, **kwargs): pass
        @property
        def plugin_name(self): return "astrbot_simplified"
        @property
        def enable_plugin(self): return True
        @property
        def dependencies(self): return []
        @property
        def python_dependencies(self): return []
        @property
        def config_file_name(self): return "config.toml"
        @property
        def config_schema(self): return {}
        def get_plugin_components(self): return []
    
    class BaseAction:
        async def execute(self, message, context): return "占位符Action"
    
    class BaseCommand:
        async def execute(self, message, context): return "占位符Command"
    
    class ActionInfo:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class CommandInfo:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class ActionActivationType:
        KEYWORD = "keyword"
        ALWAYS = "always"
        LLM_JUDGE = "llm_judge"
    
    class ChatMode:
        ALL = "all"
        PRIVATE = "private"
        GROUP = "group"
    
    def register_plugin(cls): return cls

# ============================================================================
# 核心插件类
# ============================================================================

@register_plugin
class AstrBotSimplifiedPlugin(BasePlugin):
    """
    AstrBot-MaiBot精简集成插件
    
    只包含3个核心组件：
    1. LLM服务Action - 调用AstrBot的LLM功能
    2. 状态查询Command - 查询AstrBot系统状态
    3. 系统控制Command - 基础的系统控制功能
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.initialized = False
        logger.info("🚀 AstrBot精简集成插件初始化完成")
    
    # 插件基本信息
    @property
    def plugin_name(self) -> str:
        return "astrbot_simplified"
    
    @property
    def enable_plugin(self) -> bool:
        return True
    
    @property
    def dependencies(self) -> List[str]:
        return []
    
    @property
    def python_dependencies(self) -> List:
        return []
    
    @property
    def config_file_name(self) -> str:
        return "config.toml"
    
    @property
    def config_schema(self) -> Dict[str, Any]:
        return {
            "astrbot_service": {
                "enable_llm": True,
                "default_provider": "openai",
                "default_model": "gpt-3.5-turbo"
            }
        }
    
    def get_plugin_components(self):
        """获取插件组件（只有3个核心组件）"""
        if not MAIBOT_AVAILABLE:
            logger.warning("MaiBot插件系统不可用，无法注册组件")
            return []
        
        components = []
        
        try:
            # 1. LLM服务Action
            components.append((
                ActionInfo(
                    name="astrbot_llm",
                    description="调用AstrBot的LLM服务",
                    activation_type=ActionActivationType.KEYWORD,
                    keywords=["ai", "llm", "chat"],
                    chat_mode=ChatMode.ALL
                ),
                AstrBotLLMAction
            ))
            
            # 2. 状态查询Command
            components.append((
                CommandInfo(
                    name="astrbot_status",
                    description="查询AstrBot系统状态",
                    chat_mode=ChatMode.ALL
                ),
                AstrBotStatusCommand
            ))
            
            # 3. 系统控制Command
            components.append((
                CommandInfo(
                    name="astrbot_control",
                    description="AstrBot系统控制",
                    chat_mode=ChatMode.ALL
                ),
                AstrBotControlCommand
            ))
            
            logger.info(f"✅ 成功注册 {len(components)} 个核心组件")
            
        except Exception as e:
            logger.error(f"❌ 组件注册失败: {e}")
        
        return components

# ============================================================================
# 核心组件实现
# ============================================================================

class AstrBotLLMAction(BaseAction):
    """AstrBot LLM服务Action - 核心功能"""

    async def execute(self, message, context):
        """执行LLM服务调用"""
        try:
            # 导入服务接口
            from .astrbot_service_interface import AstrBotServiceInterface

            # 提取消息内容
            content = getattr(message, 'content', str(message))

            # 调用真实的AstrBot LLM服务
            async with AstrBotServiceInterface() as service:
                result = await service.call_llm_service(content)

                if "error" in result:
                    return f"❌ LLM服务调用失败: {result['error']}"

                # 提取响应内容
                if "response" in result:
                    response = result["response"]
                elif "content" in result:
                    response = result["content"]
                else:
                    response = f"🤖 AstrBot LLM响应: {content}"

                logger.info(f"✅ LLM服务调用成功: {content[:50]}...")
                return response

        except Exception as e:
            logger.error(f"❌ LLM服务调用失败: {e}")
            return f"❌ LLM服务调用失败: {e}"

class AstrBotStatusCommand(BaseCommand):
    """AstrBot状态查询Command"""

    async def execute(self, message, context):
        """查询AstrBot系统状态"""
        try:
            # 导入服务接口
            from .astrbot_service_interface import AstrBotServiceInterface

            # 查询真实的AstrBot系统状态
            async with AstrBotServiceInterface() as service:
                status_result = await service.get_system_status()
                providers_result = await service.get_llm_providers()
                plugin_result = await service.get_plugin_info()

                if "error" in status_result:
                    return f"❌ 状态查询失败: {status_result['error']}"

                # 格式化状态信息
                status_info = []
                status_info.append(f"• 系统状态: {status_result.get('status', '未知')}")
                status_info.append(f"• LLM提供商: {len(providers_result.get('providers', []))}个可用")
                status_info.append(f"• 插件系统: {plugin_result.get('status', '未知')}")
                status_info.append(f"• 运行时间: {status_result.get('uptime', '未知')}")

                response = f"📊 AstrBot系统状态:\n" + "\n".join(status_info)

                logger.info("✅ 系统状态查询成功")
                return response

        except Exception as e:
            logger.error(f"❌ 状态查询失败: {e}")
            return f"❌ 状态查询失败: {e}"

class AstrBotControlCommand(BaseCommand):
    """AstrBot系统控制Command"""
    
    async def execute(self, message, context):
        """执行系统控制操作"""
        try:
            # 提取控制命令
            content = getattr(message, 'content', str(message))
            
            if "重启" in content:
                response = "🔄 AstrBot系统重启中..."
            elif "停止" in content:
                response = "⏹️ AstrBot系统停止中..."
            elif "配置" in content:
                response = "⚙️ AstrBot配置管理功能"
            else:
                response = "ℹ️ 可用控制命令: 重启、停止、配置"
            
            logger.info(f"✅ 系统控制执行: {content}")
            return response
            
        except Exception as e:
            logger.error(f"❌ 系统控制失败: {e}")
            return f"❌ 系统控制失败: {e}"

# ============================================================================
# 插件注册
# ============================================================================

if __name__ == "__main__":
    logger.info("🎯 AstrBot精简集成插件加载完成")
    logger.info("📦 包含组件: LLM服务Action + 状态查询Command + 系统控制Command")
